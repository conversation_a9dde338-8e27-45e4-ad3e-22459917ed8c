<template>
  <div class="aggregation-page bg-BG min-h-screen p-J3">
    <!-- 搜索和操作区域 -->
    <div class="search-section bg-BG1 rounded-Ra p-J3 mb-J3 shadow-S2">
      <div class="flex justify-between items-center">
        <!-- 左侧搜索区域 -->
        <div class="flex items-center gap-J3">
          <!-- 搜索框 -->
          <div class="search-input-wrapper relative">
            <div class="search-input flex items-center bg-BG border border-B2 rounded-Ra px-J3 py-J1 w-60 h-8">
              <div class="search-icon w-6 h-6 flex items-center justify-center">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M7.33333 12.6667C10.2789 12.6667 12.6667 10.2789 12.6667 7.33333C12.6667 4.38781 10.2789 2 7.33333 2C4.38781 2 2 4.38781 2 7.33333C2 10.2789 4.38781 12.6667 7.33333 12.6667Z" stroke="#A4ADB8" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M14 14L11.1 11.1" stroke="#A4ADB8" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="search-text flex-1 ml-J1">
                <span class="text-T4 text-Ab">请输入关键字</span>
              </div>
            </div>
          </div>

          <!-- 机组类型下拉选择 -->
          <div class="dropdown-wrapper">
            <div class="dropdown-select flex items-center justify-between bg-BG border border-B2 rounded-Ra px-J3 py-J1 w-60 h-8">
              <div class="dropdown-content flex items-center gap-J1">
                <span class="dropdown-label text-T3 text-xs">机组类型</span>
                <span class="dropdown-value text-T1 text-Ab">全部</span>
              </div>
              <div class="dropdown-arrow w-4 h-4 flex items-center justify-center">
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                  <path d="M3 4.5L6 7.5L9 4.5" stroke="#A4ADB8" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧操作区域 -->
        <div class="action-section flex items-center gap-J3">
          <button class="add-button bg-Sta1 text-BG1 px-J3 py-J0 rounded-Ra text-Ab font-normal">
            新增
          </button>
        </div>
      </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section bg-BG1 rounded-Ra shadow-S2">
      <!-- 表格头部 -->
      <div class="table-header">
        <div class="table-row flex bg-BG5 border-b border-B2">
          <div class="table-cell flex items-center justify-center px-J4 py-J2 text-T2 text-xs font-normal border-r border-B2 w-20">
            序号
          </div>
          <div class="table-cell flex items-center px-J4 py-J2 text-T2 text-xs font-normal border-r border-B2 flex-1">
            聚合商名称
          </div>
          <div class="table-cell flex items-center px-J4 py-J2 text-T2 text-xs font-normal border-r border-B2 w-32">
            聚合商类型
          </div>
          <div class="table-cell flex items-center px-J4 py-J2 text-T2 text-xs font-normal border-r border-B2 w-32">
            状态
          </div>
          <div class="table-cell flex items-center px-J4 py-J2 text-T2 text-xs font-normal border-r border-B2 w-40">
            创建时间
          </div>
          <div class="table-cell flex items-center px-J4 py-J2 text-T2 text-xs font-normal w-32">
            操作
          </div>
        </div>
      </div>

      <!-- 表格内容 -->
      <div class="table-body">
        <!-- 示例数据行 -->
        <div class="table-row flex border-b border-B2 hover:bg-BG2">
          <div class="table-cell flex items-center justify-center px-J4 py-J2 text-T1 text-Ab border-r border-B2 w-20">
            1
          </div>
          <div class="table-cell flex items-center px-J4 py-J2 text-T1 text-Ab border-r border-B2 flex-1">
            示例聚合商名称
          </div>
          <div class="table-cell flex items-center px-J4 py-J2 text-T1 text-Ab border-r border-B2 w-32">
            类型A
          </div>
          <div class="table-cell flex items-center px-J4 py-J2 border-r border-B2 w-32">
            <span class="status-tag bg-Sta1 text-BG1 px-J1 py-J0 rounded-Ra text-xs">
              正常
            </span>
          </div>
          <div class="table-cell flex items-center px-J4 py-J2 text-T1 text-Ab border-r border-B2 w-40">
            2024-01-01 10:00:00
          </div>
          <div class="table-cell flex items-center justify-end px-J4 py-J2 w-32">
            <div class="action-buttons flex items-center gap-J1">
              <button class="action-btn text-ZS text-Ab hover:text-F1">
                编辑
              </button>
              <button class="action-btn text-Sta3 text-Ab hover:text-red-600">
                删除
              </button>
            </div>
          </div>
        </div>

        <!-- 更多示例行可以在这里添加 -->
        <div class="table-row flex border-b border-B2 hover:bg-BG2">
          <div class="table-cell flex items-center justify-center px-J4 py-J2 text-T1 text-Ab border-r border-B2 w-20">
            2
          </div>
          <div class="table-cell flex items-center px-J4 py-J2 text-T1 text-Ab border-r border-B2 flex-1">
            另一个聚合商
          </div>
          <div class="table-cell flex items-center px-J4 py-J2 text-T1 text-Ab border-r border-B2 w-32">
            类型B
          </div>
          <div class="table-cell flex items-center px-J4 py-J2 border-r border-B2 w-32">
            <span class="status-tag bg-Sta2 text-BG1 px-J1 py-J0 rounded-Ra text-xs">
              警告
            </span>
          </div>
          <div class="table-cell flex items-center px-J4 py-J2 text-T1 text-Ab border-r border-B2 w-40">
            2024-01-02 14:30:00
          </div>
          <div class="table-cell flex items-center justify-end px-J4 py-J2 w-32">
            <div class="action-buttons flex items-center gap-J1">
              <button class="action-btn text-ZS text-Ab hover:text-F1">
                编辑
              </button>
              <button class="action-btn text-Sta3 text-Ab hover:text-red-600">
                删除
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section flex justify-end items-center mt-J3 px-J3">
      <div class="pagination-info text-T3 text-Ab mr-J3">
        共 2 条记录
      </div>
      <div class="pagination-controls flex items-center gap-J1">
        <button class="pagination-btn border border-B2 px-J1 py-J0 rounded-Ra text-T2 text-Ab hover:bg-BG2">
          上一页
        </button>
        <button class="pagination-btn bg-ZS text-BG1 px-J1 py-J0 rounded-Ra text-Ab">
          1
        </button>
        <button class="pagination-btn border border-B2 px-J1 py-J0 rounded-Ra text-T2 text-Ab hover:bg-BG2">
          下一页
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AggregationPage',
  data() {
    return {
      // 这里可以添加数据属性，但根据要求只做纯展示组件
    }
  }
}
</script>

<style scoped>
/* 组件特定样式 */
.aggregation-page {
  font-family: 'PingFang SC', sans-serif;
}

.search-input-wrapper {
  position: relative;
}

.dropdown-wrapper {
  position: relative;
}

.table-section {
  overflow: hidden;
}

.table-row:last-child {
  border-bottom: none;
}

.action-btn {
  transition: color 0.2s ease;
  cursor: pointer;
  background: none;
  border: none;
  padding: 4px 8px;
  border-radius: var(--Ra);
}

.action-btn:hover {
  background-color: var(--BG2);
}

.pagination-btn {
  transition: all 0.2s ease;
  cursor: pointer;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-tag {
  font-size: 12px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-section .flex {
    flex-direction: column;
    gap: var(--J2);
    align-items: stretch;
  }

  .search-section .flex > div {
    flex-direction: column;
    gap: var(--J2);
  }

  .search-input,
  .dropdown-select {
    width: 100%;
  }
}
</style>